package cn.trasen.ams.material.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.material.bean.returnStk.ReturnStkDtlResp;
import cn.trasen.ams.material.constant.MSkuConst;
import cn.trasen.ams.material.constant.OrdConst;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.ReturnStkDtlMapper;
import cn.trasen.ams.material.model.ReturnStkDtl;
import cn.trasen.ams.material.service.ReturnStkDtlService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName ReturnStkDtlServiceImpl
 * @Description TODO
 * @date 2025年8月9日 下午2:06:44
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ReturnStkDtlServiceImpl implements ReturnStkDtlService {

	private transient static final Logger logger = LoggerFactory.getLogger(ReturnStkDtlServiceImpl.class);

	@Autowired
	private ReturnStkDtlMapper mapper;

	@Autowired
	private DictService dictService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(ReturnStkDtl record) {

		if(record.getId() == null){
			record.setId(IdGeneraterUtils.nextId());
		}

		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
			record.setSsoOrgName(user.getOrgName());
			record.setDeptId(user.getDeptId());
			record.setDeptName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(ReturnStkDtl record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		ReturnStkDtl record = new ReturnStkDtl();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public ReturnStkDtl selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<ReturnStkDtl> getDataSetList(Page page, ReturnStkDtl record) {
		Example example = new Example(ReturnStkDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<ReturnStkDtl> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void batchInsert(List<ReturnStkDtl> returnStkDtlList) {
		if (returnStkDtlList == null || returnStkDtlList.isEmpty()) {
			return;
		}

		// 先删除原有明细
		String returnStkId = returnStkDtlList.get(0).getReturnStkId();
		if (returnStkId != null) {
			_deleteByReturnStkId_(returnStkId);
		}

		// 批量插入新明细
		for (ReturnStkDtl dtl : returnStkDtlList) {
			save(dtl);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void deleteByReturnStkId(String returnStkId) {
		Assert.hasText(returnStkId, "退库单ID不能为空.");
		Example example = new Example(ReturnStkDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("returnStkId", returnStkId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

		List<ReturnStkDtl> dtlList = mapper.selectByExample(example);
		for (ReturnStkDtl dtl : dtlList) {
			deleteById(dtl.getId());
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void _deleteByReturnStkId_(String returnStkId) {
		Assert.hasText(returnStkId, "退库单ID不能为空.");
		Example example = new Example(ReturnStkDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("returnStkId", returnStkId);
		mapper.deleteByExample(example);
	}

	@Override
	public List<ReturnStkDtl> getReturnStkDtlListByReturnStkId(String returnStkId) {
		Example example = new Example(ReturnStkDtl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("returnStkId", returnStkId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<ReturnStkDtl> records = mapper.selectByExample(example);
		return records;
	}

	@Override
	public List<ReturnStkDtlResp> getReturnStkDtlExtListByReturnStkId(String returnStkId, String name) {
		List<ReturnStkDtlResp> returnStkDtlRespList = mapper.getReturnStkDtlExtListByReturnStkId(returnStkId, name);
		returnStkDtlRespList.forEach(this::dataFmt);
		return returnStkDtlRespList;
	}

	@Override
	public List<ReturnStkDtlResp> getReturnStkDtlExtListByOutbId(String outbId) {
		List<ReturnStkDtlResp> returnStkDtlRespList = mapper.getReturnStkDtlExtListByOutbId(outbId);
		returnStkDtlRespList.forEach(this::dataFmt);
		return returnStkDtlRespList;
	}

	@Override
	public void dataFmt(ReturnStkDtlResp record) {
		// 单位显示
		if (record.getUnit() != null) {
			String unitShow = dictService.getItemName(CommonConst.DICT_UNIT, record.getUnit());
			record.setUnitShow(unitShow);
		}
	}
}
