package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Return;
import cn.trasen.ams.material.bean.returnOrder.ReturnInsertReq;
import cn.trasen.ams.material.bean.returnOrder.ReturnDetailResp;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ReturnService
 * @Description 退货单服务接口
 * @date 2025年8月7日 下午4:21:34
 */
public interface ReturnService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年8月7日 下午4:21:34
     * <AUTHOR>
     */
    Integer save(Return record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年8月7日 下午4:21:34
     * <AUTHOR>
     */
    Integer update(Return record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年8月7日 下午4:21:34
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Return
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年8月7日 下午4:21:34
     * <AUTHOR>
     */
    Return selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Return>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年8月7日 下午4:21:34
     * <AUTHOR>
     */
    DataSet<Return> getDataSetList(Page page, Return record);

    /**
     * @param record
     * @return String
     * @Title insert
     * @Description 新增退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    String insert(ReturnInsertReq record);

    /**
     * @param record
     * @return String
     * @Title edit
     * @Description 编辑退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    String edit(ReturnInsertReq record);

    /**
     * @param returnId
     * @return void
     * @Title remove
     * @Description 删除退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    void remove(String returnId);

    /**
     * @param returnIdList
     * @return void
     * @Title batchRemove
     * @Description 批量删除退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    void batchRemove(List<String> returnIdList);

    /**
     * @param returnId
     * @return void
     * @Title confirm
     * @Description 确认退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    void confirm(String returnId);

    /**
     * @param returnIdList
     * @return void
     * @Title batchConfirm
     * @Description 批量确认退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    void batchConfirm(List<String> returnIdList);

    /**
     * @param returnId
     * @return void
     * @Title rollbackConfirm
     * @Description 撤销确认退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    void rollbackConfirm(String returnId);

    /**
     * @param returnIdList
     * @return void
     * @Title rollbackBatchConfirm
     * @Description 批量撤销确认退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    void rollbackBatchConfirm(List<String> returnIdList);

    /**
     * @param currentId
     * @param direction
     * @return String
     * @Title getTargetReturnId
     * @Description 获取目标退货单ID（上一条/下一条/当前）
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    String getTargetReturnId(String currentId, String direction);

    /**
     * @param record
     * @return void
     * @Title dataFmt
     * @Description 数据格式化
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    void dataFmt(Return record);

    /**
     * @param returnId
     * @param direction
     * @param name
     * @return ReturnDetailResp
     * @Title getReturnDetail
     * @Description 获取退货单详情
     * @date 2025年8月7日 下午5:30:00
     * <AUTHOR>
     */
    ReturnDetailResp getReturnDetail(String returnId, String direction, String name);

    ReturnDetailResp getInbFmt(String inbId);
}
