package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.model.Return;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ReturnMapper extends Mapper<Return> {

    List<Return> getList(Page page, Return record);

    /**
     * 获取上一条退货单ID
     *
     * @param currentId 当前退货单ID
     * @return 上一条退货单ID
     */
    String getPrevId(@Param("currentId") String currentId);

    /**
     * 获取下一条退货单ID
     *
     * @param currentId 当前退货单ID
     * @return 下一条退货单ID
     */
    String getNextId(@Param("currentId") String currentId);
}