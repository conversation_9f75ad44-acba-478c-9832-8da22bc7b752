package cn.trasen.ams.material.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.returnStk.ReturnStkInsertReq;
import cn.trasen.ams.material.bean.returnStk.ReturnStkDetailResp;
import cn.trasen.ams.material.bean.returnStk.ReturnStkDtlResp;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.model.*;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.material.service.OutbService;
import cn.trasen.ams.material.service.ReturnStkDtlService;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.ReturnStkMapper;
import cn.trasen.ams.material.model.ReturnStk;
import cn.trasen.ams.material.service.ReturnStkService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName ReturnStkServiceImpl
 * @Description TODO
 * @date 2025年8月9日 下午2:06:06
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ReturnStkServiceImpl implements ReturnStkService {

	@Autowired
	private ReturnStkMapper mapper;

	@Autowired
	private ReturnStkDtlService returnStkDtlService;

	@Autowired
	private OutbService outbService;

	@Autowired
	private WarehouseService warehouseService;

	@Autowired
	private OrgService orgService;

	@Autowired
	private DictService dictService;

	@Autowired
	private SerialNoGenService serialNoGenService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(ReturnStk record) {

		if(record.getId() == null){
			record.setId(IdGeneraterUtils.nextId());
		}

		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
			record.setSsoOrgName(user.getOrgName());
			record.setDeptId(user.getDeptId());
			record.setDeptName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(ReturnStk record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		ReturnStk record = new ReturnStk();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public ReturnStk selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<ReturnStk> getDataSetList(Page page, ReturnStk record) {
		List<ReturnStk> records = mapper.getList(page, record);
		// 数据格式化
		records.forEach(this::dataFmt);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * 数据格式化
	 * @param record
	 */
	private void dataFmt(ReturnStk record) {
		// 状态显示
		if ("1".equals(record.getStat())) {
			record.setStatShow("已登记");
		} else if ("2".equals(record.getStat())) {
			record.setStatShow("已确认");
		}

		// 打印状态显示
		if ("0".equals(record.getPrintStat())) {
			record.setPrintStatShow("未打印");
		} else if ("1".equals(record.getPrintStat())) {
			record.setPrintStatShow("已打印");
		}

		// 仓库名称
		if (record.getWhId() != null) {
			Warehouse warehouse = warehouseService.selectById(record.getWhId());
			if (warehouse != null) {
				record.setWhName(warehouse.getName());
			}
		}
	}

	/**
	 * 准备数据
	 * @param record
	 */
	private void prepare(ReturnStkInsertReq record) {
		ReturnStk returnStk = record.getReturnStk();
		List<ReturnStkDtl> returnStkDtlList = record.getReturnStkDtlList();

		// 生成单据号
		if (returnStk.getFlowNo() == null || returnStk.getFlowNo().isEmpty()) {
			String flowNo = serialNoGenService.generateSerialNo("TK");
			returnStk.setFlowNo(flowNo);
		}

		// 设置退库单状态为已登记
		returnStk.setStat(OrdConst.ORD_STAT_REGISTERED);
		returnStk.setPrintStat("0"); // 未打印

		// 计算总金额
		BigDecimal totalAmt = BigDecimal.ZERO;
		for (ReturnStkDtl dtl : returnStkDtlList) {
			dtl.setReturnStkId(returnStk.getId());
			if (dtl.getPrice() != null && dtl.getNum() != null) {
				BigDecimal itemTotal = dtl.getPrice().multiply(new BigDecimal(dtl.getNum()));
				dtl.setTotalAmt(itemTotal);
				totalAmt = totalAmt.add(itemTotal);
			}
		}

		// 设置退库单的总金额
		returnStk.setTotalAmt(totalAmt);
	}

	@Transactional(readOnly = false)
	@Override
	public String insert(ReturnStkInsertReq record) {
		// 准备部分
		prepare(record);

		ReturnStk returnStk = record.getReturnStk();
		List<ReturnStkDtl> returnStkDtlList = record.getReturnStkDtlList();

		// 写入退库单
		save(returnStk);

		// 写入退库单详情
		returnStkDtlService.batchInsert(returnStkDtlList);

		return returnStk.getId();
	}

	@Transactional(readOnly = false)
	@Override
	public String edit(ReturnStkInsertReq record) {
		// 准备部分
		prepare(record);

		ReturnStk returnStk = record.getReturnStk();
		List<ReturnStkDtl> returnStkDtlList = record.getReturnStkDtlList();
		// 写入退库单
		update(returnStk);
		// 写入退库单详情
		returnStkDtlService.batchInsert(returnStkDtlList);
		return returnStk.getId();
	}

	@Override
	public ReturnStkDetailResp getReturnStkDetail(String id, String direction, String name) {
		String targetId = id;

		// 根据方向获取目标ID
		if ("prev".equals(direction)) {
			targetId = mapper.getPrevId(id);
		} else if ("next".equals(direction)) {
			targetId = mapper.getNextId(id);
		}

		if (targetId == null) {
			throw new IllegalArgumentException("没有找到相应的退库单记录");
		}

		ReturnStk returnStk = selectById(targetId);
		if (returnStk == null) {
			throw new IllegalArgumentException("退库单不存在");
		}

		ReturnStkDetailResp record = new ReturnStkDetailResp();

		// 数据格式化
		dataFmt(returnStk);

		// 获取退库单明细
		List<ReturnStkDtlResp> returnStkDtlList = returnStkDtlService.getReturnStkDtlExtListByReturnStkId(targetId, name);

		record.setReturnStk(returnStk);
		record.setReturnStkDtlList(returnStkDtlList);

		return record;
	}

	@Override
	public ReturnStkDetailResp getOutbFmt(String outbId) {
		Outb outb = outbService.selectById(outbId);

		if (outb == null) {
			throw new IllegalArgumentException("出库单不存在");
		}

		ReturnStk returnStk = new ReturnStk();
		// copy
		returnStk.setOutbId(outbId);
		returnStk.setWhId(outb.getWhId());
		returnStk.setReturnDeptId(outb.getOutDeptId());
		returnStk.setReturnDeptName(outb.getOutDeptName());
		returnStk.setReturnerId(outb.getApplyUserId());
		returnStk.setReturnerName(outb.getApplyUserName());
		returnStk.setReturnDate(new Date());

		ReturnStkDetailResp record = new ReturnStkDetailResp();

		// 数据格式化
		dataFmt(returnStk);

		// 获取退库单明细
		List<ReturnStkDtlResp> returnStkDtlList = returnStkDtlService.getReturnStkDtlExtListByOutbId(outbId);

		record.setReturnStk(returnStk);
		record.setReturnStkDtlList(returnStkDtlList);

		return record;
	}

	@Transactional(readOnly = false)
	@Override
	public void batchRemove(List<String> returnStkIdList) {
		if (CollectionUtils.isEmpty(returnStkIdList)) {
			throw new IllegalArgumentException("退库单ID列表不能为空");
		}

		for (String returnStkId : returnStkIdList) {
			ReturnStk returnStk = selectById(returnStkId);
			if (returnStk == null) {
				continue;
			}

			if (OrdConst.ORD_STAT_CHECKED.equals(returnStk.getStat())) {
				throw new IllegalArgumentException("已确认的退库单不能删除，请先回滚确认。");
			}

			deleteById(returnStkId);
			// 删除退库单明细
			returnStkDtlService.deleteByReturnStkId(returnStkId);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void batchConfirm(List<String> returnStkIdList) {
		if (CollectionUtils.isEmpty(returnStkIdList)) {
			throw new IllegalArgumentException("退库单ID列表不能为空");
		}

		for (String returnStkId : returnStkIdList) {
			ReturnStk returnStk = selectById(returnStkId);
			if (returnStk == null) {
				continue;
			}

			if (OrdConst.ORD_STAT_CHECKED.equals(returnStk.getStat())) {
				continue; // 已确认的跳过
			}

			// 更新状态为已确认
			returnStk.setStat(OrdConst.ORD_STAT_CHECKED);
			returnStk.setDoerId(UserInfoHolder.getCurrentUserInfo().getUsercode());
			returnStk.setDoerName(UserInfoHolder.getCurrentUserInfo().getUsername());
			returnStk.setDoDate(new Date());
			update(returnStk);

			// TODO: 这里可以添加库存相关的业务逻辑
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void rollbackBatchConfirm(List<String> returnStkIdList) {
		if (CollectionUtils.isEmpty(returnStkIdList)) {
			throw new IllegalArgumentException("退库单ID列表不能为空");
		}

		for (String returnStkId : returnStkIdList) {
			ReturnStk returnStk = selectById(returnStkId);
			if (returnStk == null) {
				continue;
			}

			if (!OrdConst.ORD_STAT_CHECKED.equals(returnStk.getStat())) {
				continue; // 未确认的跳过
			}

			// 更新状态为已登记
			returnStk.setStat(OrdConst.ORD_STAT_REGISTERED);
			returnStk.setDoerId(null);
			returnStk.setDoerName(null);
			returnStk.setDoDate(null);
			update(returnStk);

			// TODO: 这里可以添加库存相关的业务逻辑
		}
	}
}
