package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.bean.returnStk.ReturnStkDtlResp;
import cn.trasen.ams.material.model.ReturnStkDtl;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ReturnStkDtlMapper extends Mapper<ReturnStkDtl> {
    void batchInsert(@Param("returnStkDtlList") List<ReturnStkDtl> returnStkDtlList);

    List<ReturnStkDtlResp> getReturnStkDtlExtListByReturnStkId(@Param("returnStkId") String returnStkId, @Param("name") String name);

    List<ReturnStkDtlResp> getReturnStkDtlExtListByOutbId(@Param("outbId") String outbId);
}