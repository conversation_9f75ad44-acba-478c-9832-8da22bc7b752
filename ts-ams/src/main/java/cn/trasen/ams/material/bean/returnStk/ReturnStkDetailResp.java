package cn.trasen.ams.material.bean.returnStk;

import cn.trasen.ams.material.model.ReturnStk;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.returnStk
 * @className: ReturnStkDetailResp
 * @author: chenbin
 * @description: 退库单详情响应体
 * @date: 2025/8/9 14:30
 * @version: 1.0
 */
@Data
public class ReturnStkDetailResp {

    @ApiModelProperty(value = "退库单主数据")
    private ReturnStk returnStk;

    @ApiModelProperty(value = "退库单明细列表")
    private List<ReturnStkDtlResp> returnStkDtlList;
}
