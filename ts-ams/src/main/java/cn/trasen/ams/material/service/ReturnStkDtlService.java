package cn.trasen.ams.material.service;

import cn.trasen.ams.material.bean.returnStk.ReturnStkDtlResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnStkDtl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ReturnStkDtlService
 * @Description 退库单明细服务接口
 * @date 2025年8月9日 下午2:06:44
 */
public interface ReturnStkDtlService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    Integer save(ReturnStkDtl record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    Integer update(ReturnStkDtl record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @param id
     * @return ReturnStkDtl
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    ReturnStkDtl selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<ReturnStkDtl>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    DataSet<ReturnStkDtl> getDataSetList(Page page, ReturnStkDtl record);

    /**
     * @param returnStkDtlList
     * @return void
     * @Title batchInsert
     * @Description 批量插入退库单明细
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    void batchInsert(List<ReturnStkDtl> returnStkDtlList);

    /**
     * @param returnStkId
     * @return void
     * @Title deleteByReturnStkId
     * @Description 根据退库单ID删除明细
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    void deleteByReturnStkId(String returnStkId);

    /**
     * @param returnStkId
     * @return void
     * @Title _deleteByReturnStkId_
     * @Description 根据退库单ID物理删除明细
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    void _deleteByReturnStkId_(String returnStkId);

    /**
     * @param returnStkId
     * @return List<ReturnStkDtl>
     * @Title getReturnStkDtlListByReturnStkId
     * @Description 根据退库单ID获取明细列表
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    List<ReturnStkDtl> getReturnStkDtlListByReturnStkId(String returnStkId);

    /**
     * @param returnStkId
     * @param name
     * @return List<ReturnStkDtlResp>
     * @Title getReturnStkDtlExtListByReturnStkId
     * @Description 根据退库单ID获取扩展明细列表
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    List<ReturnStkDtlResp> getReturnStkDtlExtListByReturnStkId(String returnStkId, String name);

    /**
     * @param outbId
     * @return List<ReturnStkDtlResp>
     * @Title getReturnStkDtlExtListByOutbId
     * @Description 根据出库单ID获取退库单明细列表
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    List<ReturnStkDtlResp> getReturnStkDtlExtListByOutbId(String outbId);

    /**
     * @param record
     * @return void
     * @Title dataFmt
     * @Description 数据格式化
     * @date 2025年8月9日 下午2:06:44
     * <AUTHOR>
     */
    void dataFmt(ReturnStkDtlResp record);
}
