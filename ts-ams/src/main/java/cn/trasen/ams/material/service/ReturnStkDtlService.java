package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnStkDtl;

/**
 * @ClassName ReturnStkDtlService
 * @Description TODO
 * @date 2025年8月9日 下午2:06:44
 * <AUTHOR>
 * @version 1.0
 */
public interface ReturnStkDtlService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月9日 下午2:06:44
	 * <AUTHOR>
	 */
	Integer save(ReturnStkDtl record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月9日 下午2:06:44
	 * <AUTHOR>
	 */
	Integer update(ReturnStkDtl record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月9日 下午2:06:44
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return ReturnStkDtl
	 * @date 2025年8月9日 下午2:06:44
	 * <AUTHOR>
	 */
	ReturnStkDtl selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<ReturnStkDtl>
	 * @date 2025年8月9日 下午2:06:44
	 * <AUTHOR>
	 */
	DataSet<ReturnStkDtl> getDataSetList(Page page, ReturnStkDtl record);
}
