package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Return;
import cn.trasen.ams.material.service.ReturnService;
import cn.trasen.ams.material.bean.returnOrder.ReturnInsertReq;
import cn.trasen.ams.material.bean.returnOrder.ReturnDetailResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ReturnController
 * @Description TODO
 * @date 2025年8月7日 下午4:21:34
 */
@RestController
@Api(tags = "ReturnController")
public class ReturnController {

    private transient static final Logger logger = LoggerFactory.getLogger(ReturnController.class);

    @Autowired
    private ReturnService returnService;


    /**
     * @param page
     * @param record
     * @return DataSet<Return>
     * @Title selectReturnList
     * @Description 查询列表
     * @date 2025年8月7日 下午4:21:34
     * <AUTHOR>
     */
    @ApiOperation(value = "退货单列表", notes = "退货单列表")
    @GetMapping("/api/material/return/list")
    public DataSet<Return> selectReturnList(Page page, Return record) {
        return returnService.getDataSetList(page, record);
    }

    /**
     * @param id
     * @param direction
     * @param name
     * @return PlatformResult<ReturnDetailResp>
     * @Title selectReturnById
     * @Description 根据ID查询退货单详情
     * @date 2025年8月7日 下午5:30:00
     * <AUTHOR>
     */
    @ApiOperation(value = "退货单详情", notes = "退货单详情")
    @GetMapping("/api/material/return/{id}")
    public PlatformResult<ReturnDetailResp> selectReturnById(@PathVariable String id,
                                                             @RequestParam(value = "direction", defaultValue = "current") String direction,
                                                             @RequestParam(required = false) String name) {
        try {
            ReturnDetailResp record = returnService.getReturnDetail(id, direction, name);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "入库单转退货单", notes = "入库单转退货单")
    @GetMapping("/api/material/return/inbFmt/{id}")
    public PlatformResult<ReturnDetailResp> inbFmt(@PathVariable String id) {
        try {
            ReturnDetailResp record = returnService.getInbFmt(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveReturn
     * @Description 新增退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    @ApiOperation(value = "退货单新增", notes = "退货单新增")
    @PostMapping("/api/material/return/save")
    public PlatformResult<String> saveReturn(@Validated @RequestBody ReturnInsertReq record) {
        try {
            String returnId = returnService.insert(record);
            return PlatformResult.success(returnId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateReturn
     * @Description 编辑退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    @ApiOperation(value = "退货单编辑", notes = "退货单编辑")
    @PostMapping("/api/material/return/update")
    public PlatformResult<String> updateReturn(@Validated @RequestBody ReturnInsertReq record) {
        try {
            String returnId = returnService.edit(record);
            return PlatformResult.success(returnId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param returnIdList
     * @return PlatformResult<String>
     * @Title batchDeleteReturn
     * @Description 批量删除退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    @ApiOperation(value = "退货单批量删除", notes = "退货单批量删除")
    @PostMapping("/api/material/return/batch/delete")
    public PlatformResult<String> batchDeleteReturn(@RequestBody List<String> returnIdList) {
        try {
            returnService.batchRemove(returnIdList);
            return PlatformResult.success("成功删除 " + returnIdList.size() + " 条退货单记录");
        } catch (Exception e) {
            logger.error("批量删除退货单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param returnIdList
     * @return PlatformResult<String>
     * @Title batchConfirmReturn
     * @Description 批量确认退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    @ApiOperation(value = "退货单批量确认", notes = "退货单批量确认")
    @PostMapping("/api/material/return/batch/confirm")
    public PlatformResult<String> batchConfirmReturn(@RequestBody List<String> returnIdList) {
        try {
            returnService.batchConfirm(returnIdList);
            return PlatformResult.success("成功确认 " + returnIdList.size() + " 条退货单记录");
        } catch (Exception e) {
            logger.error("批量确认退货单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param returnIdList
     * @return PlatformResult<String>
     * @Title batchRollbackReturn
     * @Description 批量撤销确认退货单
     * @date 2025年8月7日 下午4:45:00
     * <AUTHOR>
     */
    @ApiOperation(value = "退货单批量撤销确认", notes = "退货单批量撤销确认")
    @PostMapping("/api/material/return/batch/rollback")
    public PlatformResult<String> batchRollbackReturn(@RequestBody List<String> returnIdList) {
        try {
            returnService.rollbackBatchConfirm(returnIdList);
            return PlatformResult.success("成功撤销确认 " + returnIdList.size() + " 条退货单记录");
        } catch (Exception e) {
            logger.error("批量撤销确认退货单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
