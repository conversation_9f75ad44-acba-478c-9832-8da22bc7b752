package cn.trasen.ams.material.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.material.service.*;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.returnOrder.ReturnInsertReq;
import cn.trasen.ams.material.bean.returnOrder.ReturnDetailResp;
import cn.trasen.ams.material.bean.returnOrder.ReturnDtlResp;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.constant.MethodCodeConst;
import cn.trasen.ams.material.model.*;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.service.SupplierService;
import cn.trasen.ams.common.model.Supplier;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.ReturnMapper;
import cn.trasen.ams.material.model.Return;
import com.alibaba.fastjson.JSON;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ReturnServiceImpl
 * @Description 退货单服务实现类
 * @date 2025年8月7日 下午4:21:34
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ReturnServiceImpl implements ReturnService {

    private transient static final Logger logger = LoggerFactory.getLogger(ReturnServiceImpl.class);

    @Autowired
    private ReturnMapper mapper;

    @Autowired
    private ReturnDtlService returnDtlService;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private MethodCodeService methodCodeService;

    @Autowired
    private MtdCodeRelaService mtdCodeRelaService;

    @Autowired
    private StockCurService stockCurService;

    @Autowired
    private StockSeqService stockSeqService;

    @Autowired
    private DictService dictService;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private InbService inbService;


    @Transactional(readOnly = false)
    @Override
    public Integer save(Return record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Return record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Return record = new Return();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Return selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Return> getDataSetList(Page page, Return record) {
        List<Return> records = mapper.getList(page, record);
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 编辑和删除的准备工作
     * @date 2025/8/7 16:50
     */
    @Transactional(readOnly = false)
    public void prepare(ReturnInsertReq record) {
        Return returnOrder = record.getReturnOrder();
        List<ReturnDtl> returnDtlList = record.getReturnDtlList();

        // 新增
        if (returnOrder.getId() == null) {
            if (returnOrder.getMtdCodeId() == null) {
                // 默认方式吗
                MethodCode methodCode = methodCodeService.selectByMethod(OrdConst.ORD_TYPE_TH);
                if (methodCode == null) {
                    throw new RuntimeException("退货单默认方式码不存在，请配置");
                }
                returnOrder.setMtdCodeId(methodCode.getId());
                returnOrder.setMtdCodeName(methodCode.getName());
            }
            // 生成退货单流水号
            if (returnOrder.getMtdCodeId() != null) {
                String flowNo = methodCodeService.genFlowNo(returnOrder.getMtdCodeId());
                returnOrder.setFlowNo(flowNo);
            }
            returnOrder.setId(IdGeneraterUtils.nextId());
            // 默认状态
            returnOrder.setStat(OrdConst.ORD_STAT_REGED);
            returnOrder.setPrintStat(CommonConst.NO);
            returnOrder.setReturnerId(UserInfoHolder.getCurrentUserInfo().getUsercode());
            returnOrder.setReturnerName(UserInfoHolder.getCurrentUserInfo().getUsername());
        } else {
            // 删除之前的明细
            returnDtlService._deleteByReturnId_(returnOrder.getId());
            // 清空方式码的关系
            mtdCodeRelaService._deleteByModelId_(OrdConst.ORD_TYPE_TH, returnOrder.getId());
        }

        // 计算总金额
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (ReturnDtl returnDtl : returnDtlList) {
            returnDtl.setReturnId(returnOrder.getId());
            returnDtl.setId(IdGeneraterUtils.nextId());

            // 计算明细总金额
            if (returnDtl.getPrice() != null && returnDtl.getNum() != null) {
                BigDecimal dtlTotalAmt = returnDtl.getPrice().multiply(new BigDecimal(returnDtl.getNum()));
                returnDtl.setTotalAmt(dtlTotalAmt);
                totalAmt = totalAmt.add(dtlTotalAmt);
            }
        }

        // 设置退货单的总金额
        returnOrder.setTotalAmt(totalAmt);

        // 插入与方式码的关系
        if (returnOrder.getMtdCodeId() != null) {
            MethodCode methodCode = methodCodeService.selectById(returnOrder.getMtdCodeId());
            MtdCodeRela mtdCodeRela = new MtdCodeRela();
            mtdCodeRela.setMtdCodeId(returnOrder.getMtdCodeId());
            mtdCodeRela.setModelType(OrdConst.ORD_TYPE_TH);
            mtdCodeRela.setModelId(returnOrder.getId());
            mtdCodeRela.setMtdCodeJson(JSON.toJSONString(methodCode));
            mtdCodeRelaService.save(mtdCodeRela);
        }
    }

    /**
     * 生成退货单流水号
     *
     * @return 流水号
     */
    private String genFlowNo() {
        return serialNoGenService.genByDate("TK");
    }

    @Transactional(readOnly = false)
    @Override
    public String insert(ReturnInsertReq record) {
        // 准备部分
        prepare(record);

        Return returnOrder = record.getReturnOrder();
        List<ReturnDtl> returnDtlList = record.getReturnDtlList();

        // 写入退货单
        save(returnOrder);

        // 写入退货单详情
        returnDtlService.batchInsert(returnDtlList);

        // TODO 如果是根据入库单进行退货，则需要验证库存 和 把退货状态更新

        return returnOrder.getId();
    }

    @Transactional(readOnly = false)
    @Override
    public String edit(ReturnInsertReq record) {
        // 准备部分
        prepare(record);

        Return returnOrder = record.getReturnOrder();
        List<ReturnDtl> returnDtlList = record.getReturnDtlList();
        // 写入退货单
        update(returnOrder);
        // 写入退货单详情
        returnDtlService.batchInsert(returnDtlList);
        return returnOrder.getId();
    }

    @Transactional(readOnly = false)
    @Override
    public void remove(String returnId) {
        Assert.hasText(returnId, "退货单ID不能为空.");
        Return returnOrder = selectById(returnId);

        if (OrdConst.ORD_STAT_CHECKED.equals(returnOrder.getStat())) {
            throw new IllegalArgumentException("已确认的退货单不能删除，请先回滚确认。");
        }

        deleteById(returnId);
        // 删除退货单明细
        returnDtlService.deleteByReturnId(returnId);
        // 删除与方式码的关系
        mtdCodeRelaService.deleteByModelId(OrdConst.ORD_TYPE_TH, returnId);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchRemove(List<String> returnIdList) {
        if (CollectionUtils.isEmpty(returnIdList)) {
            logger.warn("批量删除退货单时，传入的ID列表为空，操作被忽略.");
            return;
        }

        // 逐个处理每个退货单的删除
        for (String returnId : returnIdList) {
            try {
                remove(returnId);
            } catch (Exception e) {
                // 记录错误但继续处理其他单据
                throw new RuntimeException("退货单 " + returnId + " 删除失败: " + e.getMessage(), e);
            }
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void confirm(String returnId) {
        Assert.hasText(returnId, "退货单ID不能为空.");

        Return returnOrder = selectById(returnId);
        if (returnOrder == null) {
            throw new IllegalArgumentException("退货单不存在");
        }

        if (OrdConst.ORD_STAT_CHECKED.equals(returnOrder.getStat())) {
            throw new IllegalArgumentException("退货单已确认，不能重复确认");
        }

        // 获取退货单明细
        List<ReturnDtl> returnDtlList = returnDtlService.getReturnDtlListByReturnId(returnId);
        if (CollectionUtils.isEmpty(returnDtlList)) {
            throw new IllegalArgumentException("退货单明细数据缺失，无法完成确认");
        }

        // 获取方式码信息
        MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_TH, returnId);
        if (methodCode == null) {
            throw new IllegalArgumentException("当前出库单没有对应的出库方式，无法完成确认");
        }

        // 构造库存数据（根据方式码类型进行库存操作）
        List<StockCur> stockCurList = new ArrayList<>();
        List<StockSeq> stockSeqList = new ArrayList<>();

        for (ReturnDtl returnDtl : returnDtlList) {
            // 构建库存当前量记录
            StockCur stockCur = new StockCur();
            stockCur.setWhId(returnOrder.getWhId());
            stockCur.setSkuId(returnDtl.getSkuId());
            stockCur.setBatchNo(returnDtl.getBatchNo());

            Integer symbol = -1;
            // 根据方式码类型进行库存操作
            if (MethodCodeConst.METHOD_CODE_TYPE_DEC.equals(methodCode.getType())) {
                // 减少库存
                symbol = -1;
            } else if (MethodCodeConst.METHOD_CODE_TYPE_INC.equals(methodCode.getType())) {
                // 增加库存
                symbol = 1;
            }

            stockCur.setNum(returnDtl.getNum() * symbol);
            stockCurList.add(stockCur);

            // 构建库存流水记录
            StockSeq stockSeq = new StockSeq();
            stockSeq.setSkuId(returnDtl.getSkuId());
            stockSeq.setWhId(returnOrder.getWhId());
            stockSeq.setBatchNo(returnDtl.getBatchNo());
            stockSeq.setNum(stockCur.getNum());
            stockSeq.setOrdId(returnId);
            stockSeq.setOrdDtlId(returnDtl.getId());
            stockSeq.setOrdType(OrdConst.ORD_TYPE_TH);
            stockSeqList.add(stockSeq);
        }

        // 更新库存
        stockCurService.updateStock(stockCurList);
        // 插入库存流水
        stockSeqService.batchInsert(stockSeqList);

        // 更新退货单状态
        returnOrder.setStat(OrdConst.ORD_STAT_CHECKED);
        returnOrder.setDoTime(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            returnOrder.setDoerId(user.getUsercode());
            returnOrder.setDoerName(user.getUsername());
        }
        update(returnOrder);

        logger.info("退货单 {} 确认成功", returnId);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchConfirm(List<String> returnIdList) {
        if (CollectionUtils.isEmpty(returnIdList)) {
            logger.warn("批量确认退货单时，传入的ID列表为空，操作被忽略.");
            return;
        }

        // 逐个处理每个退货单的确认
        for (String returnId : returnIdList) {
            try {
                confirm(returnId);
            } catch (Exception e) {
                // 记录错误但继续处理其他单据
                throw new RuntimeException("退货单 " + returnId + " 确认失败: " + e.getMessage(), e);
            }
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void rollbackConfirm(String returnId) {
        Assert.hasText(returnId, "退货单ID不能为空.");

        Return returnOrder = selectById(returnId);
        if (returnOrder == null) {
            throw new IllegalArgumentException("退货单不存在");
        }

        // 检查退货单状态，只有已确认的单据才能回滚
        if (!OrdConst.ORD_STAT_CHECKED.equals(returnOrder.getStat())) {
            throw new IllegalArgumentException("只有已确认的退货单才能进行回滚操作");
        }

        // 获取退货单明细
        List<ReturnDtl> returnDtlList = returnDtlService.getReturnDtlListByReturnId(returnId);
        if (CollectionUtils.isEmpty(returnDtlList)) {
            throw new IllegalArgumentException("退货单明细数据缺失，无法完成回滚");
        }

        // 获取方式码信息
        MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_TH, returnId);
        if (methodCode == null) {
            throw new IllegalArgumentException("当前退货单没有对应的退货方式，无法完成回滚");
        }

        // 构造库存回滚数据（与确认时相反的操作）
        List<StockCur> stockCurList = new ArrayList<>();
        List<StockSeq> stockSeqList = new ArrayList<>();

        returnDtlList.forEach(returnDtl -> {
            StockCur stockCur = new StockCur();
            stockCur.setSkuId(returnDtl.getSkuId());
            stockCur.setWhId(returnOrder.getWhId());
            stockCur.setBatchNo(returnDtl.getBatchNo());

            Integer symbol = 1;
            // 回滚退货操作：根据方式码类型进行相反操作
            if (MethodCodeConst.METHOD_CODE_TYPE_DEC.equals(methodCode.getType())) {
                // 原来是减少库存，现在要增加库存
                symbol = 1;
            } else if (MethodCodeConst.METHOD_CODE_TYPE_INC.equals(methodCode.getType())) {
                // 原来是增加库存，现在要减少库存
                symbol = -1;
            }

            stockCur.setNum(symbol * returnDtl.getNum());
            stockCurList.add(stockCur);

            // 构造库存流水记录
            StockSeq stockSeq = new StockSeq();
            stockSeq.setSkuId(returnDtl.getSkuId());
            stockSeq.setWhId(returnOrder.getWhId());
            stockSeq.setBatchNo(returnDtl.getBatchNo());
            stockSeq.setNum(stockCur.getNum());
            stockSeq.setOrdType(OrdConst.ORD_TYPE_TH);
            stockSeq.setOrdId(returnId);
            stockSeq.setOrdDtlId(returnDtl.getId());
            stockSeqList.add(stockSeq);
        });

        // 更新实时库存
        stockCurService.updateStock(stockCurList);
        // 添加库存时序记录
        stockSeqService.batchInsert(stockSeqList);

        // 更新退货单状态为已登记
        returnOrder.setStat(OrdConst.ORD_STAT_REGED);
        returnOrder.setDoerId(null);
        returnOrder.setDoerName(null);
        returnOrder.setDoTime(null);

        // 设置回滚操作人信息
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            returnOrder.setCancelerId(user.getUsercode());
            returnOrder.setCancelerName(user.getUsername());
            returnOrder.setCancelTime(new Date());
        }

        update(returnOrder);

        logger.info("退货单 {} 回滚确认成功", returnId);
    }

    @Transactional(readOnly = false)
    @Override
    public void rollbackBatchConfirm(List<String> returnIdList) {
        if (CollectionUtils.isEmpty(returnIdList)) {
            logger.warn("批量撤销确认退货单时，传入的ID列表为空，操作被忽略.");
            return;
        }

        // 逐个处理每个退货单的回滚
        for (String returnId : returnIdList) {
            try {
                rollbackConfirm(returnId);
            } catch (Exception e) {
                // 记录错误但继续处理其他单据
                throw new RuntimeException("退货单 " + returnId + " 回滚失败: " + e.getMessage(), e);
            }
        }
    }

    @Override
    public String getTargetReturnId(String currentId, String direction) {
        Assert.hasText(currentId, "当前退货单ID不能为空");
        Assert.hasText(direction, "方向参数不能为空");

        switch (direction.toLowerCase()) {
            case "current":
                // 验证当前ID是否存在
                Return currentReturn = mapper.selectByPrimaryKey(currentId);
                return (currentReturn != null && !"Y".equals(currentReturn.getIsDeleted())) ? currentId : null;

            case "prev":
                // 获取上一条记录
                return mapper.getPrevId(currentId);

            case "next":
                // 获取下一条记录
                return mapper.getNextId(currentId);

            default:
                throw new IllegalArgumentException("不支持的方向参数: " + direction + "，支持的值: current, prev, next");
        }
    }

    @Override
    public void dataFmt(Return record) {
        if (record == null) {
            return;
        }

        // 格式化状态显示
        record.setStatShow(dictService.cgetNameByValue(OrdConst.ORD_STAT, record.getStat()));
        record.setPrintStatShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getPrintStat()));

        if (record.getInbId() != null) {

            Inb inb = inbService.selectById(record.getInbId());
            if (inb != null) {
                record.setInbFlowNo(inb.getFlowNo());
            }
        }

        // 格式化仓库名称
        if (!StringUtils.isEmpty(record.getWhId())) {
            Warehouse warehouse = warehouseService.selectById(record.getWhId());
            if (warehouse != null) {
                record.setWhName(warehouse.getName());
            }
        }

        // 格式化供应商名称
        if (!StringUtils.isEmpty(record.getSupplyId())) {
            Supplier supplier = supplierService.selectById(record.getSupplyId());
            if (supplier != null) {
                record.setSupplyName(supplier.getName());
            }
        }

    }

    @Override
    public ReturnDetailResp getReturnDetail(String returnId, String direction, String name) {
        // 根据direction参数获取对应的退货单ID
        String targetId = getTargetReturnId(returnId, direction);
        if (targetId == null) {
            throw new IllegalArgumentException("没有找到" + getDirectionDesc(direction) + "的记录");
        }

        ReturnDetailResp record = new ReturnDetailResp();
        Return returnOrder = selectById(targetId);

        // 获取方式码信息
        MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_TH, returnOrder.getId());
        if (methodCode != null) {
            returnOrder.setMtdCodeId(methodCode.getId());
            returnOrder.setMtdCodeName(methodCode.getName());
        }

        // 数据格式化
        dataFmt(returnOrder);

        // 获取退货单明细
        List<ReturnDtlResp> returnDtlList = returnDtlService.getReturnDtlExtListByReturnId(returnOrder.getId(), name);

        record.setReturnOrder(returnOrder);
        record.setReturnDtlList(returnDtlList);

        return record;
    }

    @Override
    public ReturnDetailResp getInbFmt(String inbId) {


        Inb inb = inbService.selectById(inbId);

        if (inb == null) {
            throw new IllegalArgumentException("入库单不存在");
        }

        Return returnOrder = new Return();
        // copy
        //BeanUtils.copyProperties(inb, returnOrder);
        returnOrder.setSupplyId(inb.getSupplyId());
        returnOrder.setWhId(inb.getWhId());
        returnOrder.setInbId(inbId);
        ReturnDetailResp record = new ReturnDetailResp();

        // 数据格式化
        dataFmt(returnOrder);

        // 获取退货单明细
        List<ReturnDtlResp> returnDtlList = returnDtlService.getReturnDtlExtListByInbId(inbId);

        record.setReturnOrder(returnOrder);
        record.setReturnDtlList(returnDtlList);

        return record;
    }

    /**
     * 获取方向描述
     *
     * @param direction 方向
     * @return 描述
     */
    private String getDirectionDesc(String direction) {
        switch (direction) {
            case "prev":
                return "上一条";
            case "next":
                return "下一条";
            case "current":
            default:
                return "当前";
        }
    }
}
