<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.ReturnStkMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.ReturnStk">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="outb_id" jdbcType="VARCHAR" property="outbId" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="wh_id" jdbcType="VARCHAR" property="whId" />
    <result column="return_dept_id" jdbcType="VARCHAR" property="returnDeptId" />
    <result column="return_dept_name" jdbcType="VARCHAR" property="returnDeptName" />
    <result column="returner_id" jdbcType="VARCHAR" property="returnerId" />
    <result column="returner_name" jdbcType="VARCHAR" property="returnerName" />
    <result column="return_date" jdbcType="DATE" property="returnDate" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="stat" jdbcType="CHAR" property="stat" />
    <result column="print_stat" jdbcType="CHAR" property="printStat" />
    <result column="doer_id" jdbcType="VARCHAR" property="doerId" />
    <result column="doer_name" jdbcType="VARCHAR" property="doerName" />
    <result column="do_date" jdbcType="TIMESTAMP" property="doDate" />
    <result column="canceler_id" jdbcType="VARCHAR" property="cancelerId" />
    <result column="canceler_name" jdbcType="VARCHAR" property="cancelerName" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <select id="getList" parameterType="cn.trasen.ams.material.model.ReturnStk" resultType="cn.trasen.ams.material.model.ReturnStk">
      SELECT
      t1.*,
      t2.name as wh_name,
      t3.flow_no as outb_flow_no
      FROM m_return_stk t1
      LEFT JOIN c_warehouse t2 ON t1.wh_id = t2.id AND t2.is_deleted = 'N'
      LEFT JOIN m_outb t3 ON t1.outb_id = t3.id AND t3.is_deleted = 'N'
      WHERE t1.is_deleted = 'N'
      <if test="flowNo != null and flowNo != ''">
          AND t1.flow_no LIKE CONCAT('%', #{flowNo}, '%')
      </if>
      <if test="whId != null and whId != ''">
          AND t1.wh_id = #{whId}
      </if>
      <if test="returnDeptName != null and returnDeptName != ''">
          AND t1.return_dept_name LIKE CONCAT('%', #{returnDeptName}, '%')
      </if>
      <if test="returnerName != null and returnerName != ''">
          AND t1.returner_name LIKE CONCAT('%', #{returnerName}, '%')
      </if>
      <if test="stat != null and stat != ''">
          AND t1.stat = #{stat}
      </if>
      <if test="returnDate != null">
          AND DATE(t1.return_date) = DATE(#{returnDate})
      </if>
      ORDER BY t1.create_date DESC
  </select>

  <select id="getPrevId" parameterType="string" resultType="string">
      SELECT id FROM m_return_stk
      WHERE is_deleted = 'N'
      AND create_date &lt; (SELECT create_date FROM m_return_stk WHERE id = #{currentId})
      ORDER BY create_date DESC
      LIMIT 1
  </select>

  <select id="getNextId" parameterType="string" resultType="string">
      SELECT id FROM m_return_stk
      WHERE is_deleted = 'N'
      AND create_date &gt; (SELECT create_date FROM m_return_stk WHERE id = #{currentId})
      ORDER BY create_date ASC
      LIMIT 1
  </select>
</mapper>