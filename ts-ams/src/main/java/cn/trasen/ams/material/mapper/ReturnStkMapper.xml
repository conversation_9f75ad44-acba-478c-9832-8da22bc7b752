<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.ReturnStkMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.ReturnStk">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="outb_id" jdbcType="VARCHAR" property="outbId" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="wh_id" jdbcType="VARCHAR" property="whId" />
    <result column="return_dept_id" jdbcType="VARCHAR" property="returnDeptId" />
    <result column="return_dept_name" jdbcType="VARCHAR" property="returnDeptName" />
    <result column="returner_id" jdbcType="VARCHAR" property="returnerId" />
    <result column="returner_name" jdbcType="VARCHAR" property="returnerName" />
    <result column="return_date" jdbcType="DATE" property="returnDate" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="stat" jdbcType="CHAR" property="stat" />
    <result column="print_stat" jdbcType="CHAR" property="printStat" />
    <result column="doer_id" jdbcType="VARCHAR" property="doerId" />
    <result column="doer_name" jdbcType="VARCHAR" property="doerName" />
    <result column="do_time" jdbcType="TIMESTAMP" property="doTime" />
    <result column="canceler_id" jdbcType="VARCHAR" property="cancelerId" />
    <result column="canceler_name" jdbcType="VARCHAR" property="cancelerName" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
</mapper>