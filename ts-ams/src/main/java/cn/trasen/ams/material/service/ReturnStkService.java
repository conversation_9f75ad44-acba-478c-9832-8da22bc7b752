package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnStk;
import cn.trasen.ams.material.bean.returnStk.ReturnStkInsertReq;
import cn.trasen.ams.material.bean.returnStk.ReturnStkDetailResp;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ReturnStkService
 * @Description 退库单服务接口
 * @date 2025年8月9日 下午2:06:06
 */
public interface ReturnStkService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    Integer save(ReturnStk record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    Integer update(ReturnStk record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @param id
     * @return ReturnStk
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    ReturnStk selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<ReturnStk>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    DataSet<ReturnStk> getDataSetList(Page page, ReturnStk record);

    /**
     * @param record
     * @return String
     * @Title insert
     * @Description 新增退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    String insert(ReturnStkInsertReq record);

    /**
     * @param record
     * @return String
     * @Title edit
     * @Description 编辑退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    String edit(ReturnStkInsertReq record);

    /**
     * @param id
     * @param direction
     * @param name
     * @return ReturnStkDetailResp
     * @Title getReturnStkDetail
     * @Description 获取退库单详情
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    ReturnStkDetailResp getReturnStkDetail(String id, String direction, String name);

    /**
     * @param outbId
     * @return ReturnStkDetailResp
     * @Title getOutbFmt
     * @Description 出库单转退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    ReturnStkDetailResp getOutbFmt(String outbId);

    /**
     * @param returnStkIdList
     * @return void
     * @Title batchRemove
     * @Description 批量删除退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    void batchRemove(List<String> returnStkIdList);

    /**
     * @param returnStkIdList
     * @return void
     * @Title batchConfirm
     * @Description 批量确认退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    void batchConfirm(List<String> returnStkIdList);

    /**
     * @param returnStkIdList
     * @return void
     * @Title rollbackBatchConfirm
     * @Description 批量撤销确认退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    void rollbackBatchConfirm(List<String> returnStkIdList);
}
