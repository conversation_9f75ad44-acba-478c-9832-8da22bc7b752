package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnStk;

/**
 * @ClassName ReturnStkService
 * @Description TODO
 * @date 2025年8月9日 下午2:06:06
 * <AUTHOR>
 * @version 1.0
 */
public interface ReturnStkService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月9日 下午2:06:06
	 * <AUTHOR>
	 */
	Integer save(ReturnStk record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月9日 下午2:06:06
	 * <AUTHOR>
	 */
	Integer update(ReturnStk record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月9日 下午2:06:06
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return ReturnStk
	 * @date 2025年8月9日 下午2:06:06
	 * <AUTHOR>
	 */
	ReturnStk selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<ReturnStk>
	 * @date 2025年8月9日 下午2:06:06
	 * <AUTHOR>
	 */
	DataSet<ReturnStk> getDataSetList(Page page, ReturnStk record);
}
