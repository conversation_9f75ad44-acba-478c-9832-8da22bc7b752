package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnStk;
import cn.trasen.ams.material.service.ReturnStkService;
import cn.trasen.ams.material.bean.returnStk.ReturnStkInsertReq;
import cn.trasen.ams.material.bean.returnStk.ReturnStkDetailResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ReturnStkController
 * @Description 退库单控制器
 * @date 2025年8月9日 下午2:06:06
 */
@RestController
@Api(tags = "ReturnStkController")
public class ReturnStkController {

    private transient static final Logger logger = LoggerFactory.getLogger(ReturnStkController.class);

    @Autowired
    private ReturnStkService returnStkService;


    /**
     * @param page
     * @param record
     * @return DataSet<ReturnStk>
     * @Title selectReturnStkList
     * @Description 查询列表
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    @ApiOperation(value = "退库单列表", notes = "退库单列表")
    @GetMapping("/api/material/returnStk/list")
    public DataSet<ReturnStk> selectReturnStkList(Page page, ReturnStk record) {
        return returnStkService.getDataSetList(page, record);
    }

    /**
     * @param id
     * @param direction
     * @param name
     * @return PlatformResult<ReturnStkDetailResp>
     * @Title selectReturnStkById
     * @Description 根据ID查询退库单详情
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    @ApiOperation(value = "退库单详情", notes = "退库单详情")
    @GetMapping("/api/material/returnStk/{id}")
    public PlatformResult<ReturnStkDetailResp> selectReturnStkById(@PathVariable String id,
                                                                   @RequestParam(value = "direction", defaultValue = "current") String direction,
                                                                   @RequestParam(required = false) String name) {
        try {
            ReturnStkDetailResp record = returnStkService.getReturnStkDetail(id, direction, name);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "出库单转退库单", notes = "出库单转退库单")
    @GetMapping("/api/material/returnStk/outbFmt/{id}")
    public PlatformResult<ReturnStkDetailResp> outbFmt(@PathVariable String id) {
        try {
            ReturnStkDetailResp record = returnStkService.getOutbFmt(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveReturnStk
     * @Description 新增退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    @ApiOperation(value = "退库单新增", notes = "退库单新增")
    @PostMapping("/api/material/returnStk/save")
    public PlatformResult<String> saveReturnStk(@Validated @RequestBody ReturnStkInsertReq record) {
        try {
            String returnStkId = returnStkService.insert(record);
            return PlatformResult.success(returnStkId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateReturnStk
     * @Description 编辑退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    @ApiOperation(value = "退库单编辑", notes = "退库单编辑")
    @PostMapping("/api/material/returnStk/update")
    public PlatformResult<String> updateReturnStk(@Validated @RequestBody ReturnStkInsertReq record) {
        try {
            String returnStkId = returnStkService.edit(record);
            return PlatformResult.success(returnStkId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param returnStkIdList
     * @return PlatformResult<String>
     * @Title batchDeleteReturnStk
     * @Description 批量删除退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    @ApiOperation(value = "退库单批量删除", notes = "退库单批量删除")
    @PostMapping("/api/material/returnStk/batch/delete")
    public PlatformResult<String> batchDeleteReturnStk(@RequestBody List<String> returnStkIdList) {
        try {
            returnStkService.batchRemove(returnStkIdList);
            return PlatformResult.success("成功删除 " + returnStkIdList.size() + " 条退库单记录");
        } catch (Exception e) {
            logger.error("批量删除退库单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param returnStkIdList
     * @return PlatformResult<String>
     * @Title batchConfirmReturnStk
     * @Description 批量确认退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    @ApiOperation(value = "退库单批量确认", notes = "退库单批量确认")
    @PostMapping("/api/material/returnStk/batch/confirm")
    public PlatformResult<String> batchConfirmReturnStk(@RequestBody List<String> returnStkIdList) {
        try {
            returnStkService.batchConfirm(returnStkIdList);
            return PlatformResult.success("成功确认 " + returnStkIdList.size() + " 条退库单记录");
        } catch (Exception e) {
            logger.error("批量确认退库单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param returnStkIdList
     * @return PlatformResult<String>
     * @Title batchRollbackReturnStk
     * @Description 批量撤销确认退库单
     * @date 2025年8月9日 下午2:06:06
     * <AUTHOR>
     */
    @ApiOperation(value = "退库单批量撤销确认", notes = "退库单批量撤销确认")
    @PostMapping("/api/material/returnStk/batch/rollback")
    public PlatformResult<String> batchRollbackReturnStk(@RequestBody List<String> returnStkIdList) {
        try {
            returnStkService.rollbackBatchConfirm(returnStkIdList);
            return PlatformResult.success("成功撤销确认 " + returnStkIdList.size() + " 条退库单记录");
        } catch (Exception e) {
            logger.error("批量撤销确认退库单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}
