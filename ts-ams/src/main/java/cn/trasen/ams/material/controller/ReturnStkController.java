package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.ReturnStk;
import cn.trasen.ams.material.service.ReturnStkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName ReturnStkController
 * @Description TODO
 * @date 2025年8月9日 下午2:06:06
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "ReturnStkController")
public class ReturnStkController {

	private transient static final Logger logger = LoggerFactory.getLogger(ReturnStkController.class);

	@Autowired
	private ReturnStkService returnStkService;

	 
}
